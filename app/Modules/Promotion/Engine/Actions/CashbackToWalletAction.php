<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Engine\Actions;

use App\Models\Clinic;
use App\Models\WalletDefinition;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Models\PromotionRule;
use App\Services\WalletService;
use Exception;
use Illuminate\Support\Facades\Log;

final readonly class CashbackToWalletAction implements Action
{
    public function __construct(private WalletService $walletService) {}

    public function apply(PromotionRule $rule, Context $context, array $config): void
    {
        $walletDefinitionId = $config['wallet_definition_id'] ?? null;
        $cashbackType = $config['cashback_type'] ?? 'fixed'; // 'fixed' or 'percentage'
        $cashbackValueRaw = (float) ($config['cashback_value'] ?? 0); // Raw value from Nova form
        $maxCashback = isset($config['max_cashback']) ? (int) $config['max_cashback'] : null; // Cast to int if present

        // Convert cashback value based on type
        $cashbackValue = match ($cashbackType) {
            'fixed' => (int) round($cashbackValueRaw * 100), // Convert dollars to cents
            'percentage' => (int) round($cashbackValueRaw * 100), // Convert percentage to basis points (e.g., 5.0% = 500 basis points)
            default => 0,
        };

        if (! $walletDefinitionId || $cashbackValue <= 0) {
            return;
        }

        // Calculate the actual cashback amount based on type
        $cashbackAmount = $this->calculateCashbackAmount($cashbackType, $cashbackValue, $context, $maxCashback);

        if ($cashbackAmount <= 0) {
            return;
        }

        $walletDefinition = WalletDefinition::find($walletDefinitionId);
        if (! $walletDefinition) {
            Log::error('Wallet definition not found for cashback', [
                'wallet_definition_id' => $walletDefinitionId,
                'rule_id' => $rule->id,
            ]);

            return;
        }

        $clinic = Clinic::find($context->clinicId);
        if (! $clinic) {
            Log::error('Clinic not found for cashback', [
                'clinic_id' => $context->clinicId,
                'rule_id' => $rule->id,
            ]);

            return;
        }

        try {
            // Get or create wallet for the clinic
            $wallet = $this->walletService->getOrCreateWallet($clinic, $walletDefinition);

            // Credit the cashback amount to the wallet
            $transaction = $this->walletService->creditWallet(
                $wallet,
                $cashbackAmount,
                "Cashback from promotion: {$rule->promotion->name}",
                'Promotion',
                $rule->promotion->id,
                [
                    'promotion_rule_id' => $rule->id,
                    'promotion_name' => $rule->promotion->name,
                    'cashback_type' => $cashbackType,
                    'cashback_value' => $cashbackValue,
                    'max_cashback' => $maxCashback,
                    'cashback_amount' => $cashbackAmount,
                    'order_total' => $context->orderTotal,
                ]
            );

        } catch (Exception $e) {
            Log::error('Failed to credit cashback to wallet', [
                'clinic_id' => $context->clinicId,
                'wallet_definition_id' => $walletDefinitionId,
                'cashback_type' => $cashbackType,
                'cashback_value' => $cashbackValue,
                'max_cashback' => $maxCashback,
                'cashback_amount' => $cashbackAmount,
                'order_total' => $context->orderTotal,
                'rule_id' => $rule->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Calculate the actual cashback amount based on type and value
     */
    private function calculateCashbackAmount(string $cashbackType, int $cashbackValue, Context $context, ?int $maxCashback = null): int
    {
        return match ($cashbackType) {
            'fixed' => $this->calculateFixedCashback($cashbackValue, $maxCashback),
            'percentage' => $this->calculatePercentageCashback($cashbackValue, $context, $maxCashback),
            default => 0,
        };
    }

    /**
     * Calculate fixed cashback with optional cap
     */
    private function calculateFixedCashback(int $fixedAmount, ?int $maxCashback = null): int
    {
        $cashbackAmount = $fixedAmount;

        // Apply maximum cap if specified
        if ($maxCashback && $cashbackAmount > $maxCashback) {
            $cashbackAmount = $maxCashback;
        }

        return $cashbackAmount;
    }

    /**
     * Calculate percentage-based cashback
     */
    private function calculatePercentageCashback(int $percentage, Context $context, ?int $maxCashback = null): int
    {
        if (! $context->orderTotal || $context->orderTotal <= 0) {
            return 0;
        }

        // Calculate percentage of order total (percentage is in basis points, e.g., 500 = 5%)
        $cashbackAmount = (int) round(($context->orderTotal * $percentage) / 10000);

        // Apply maximum cap if specified
        if ($maxCashback && $cashbackAmount > $maxCashback) {
            $cashbackAmount = $maxCashback;
        }

        return $cashbackAmount;
    }
}
