<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Engine;

final class Context
{
    public array $freeProducts = [];

    public function __construct(
        public string $clinicId,
        public ?array $cart = null, // ['product_id' => quantity, ...]
        public ?int $orderTotal = null, // Order total in cents
        public ?string $orderId = null // Order ID for reference
    ) {}
}
