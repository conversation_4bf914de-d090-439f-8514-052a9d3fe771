<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Models;

use App\Modules\Promotion\Engine\Actions\CashbackToWalletAction;
use App\Modules\Promotion\Engine\Actions\GiveFreeProductAction;
use App\Modules\Promotion\Engine\Actions\UpdateRebateEstimate;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\ActionType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use InvalidArgumentException;

final class PromotionAction extends Model
{
    use HasFactory;
    use HasUuids;

    protected $guarded = [];

    public function rule(): BelongsTo
    {
        return $this->belongsTo(PromotionRule::class, 'promotion_rule_id');
    }

    /**
     * @throws InvalidArgumentException
     */
    public function apply(Context $context): void
    {
        $action = match ($this->type) {
            ActionType::UpdateRebateEstimate => app(UpdateRebateEstimate::class),
            ActionType::GiveFreeProduct => app(GiveFreeProductAction::class),
            ActionType::CashbackToWallet => app(CashbackToWalletAction::class),
            default => throw new InvalidArgumentException('Invalid action type'),
        };

        $action->apply($this->rule, $context, $this->config);
    }

    protected function casts(): array
    {
        return [
            'type' => ActionType::class,
            'config' => 'json',
        ];
    }
}
