<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Nova;

use App\Nova\Order;
use App\Modules\Promotion\Nova\Promotion;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Code;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;

final class OrderPromotion extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Modules\Promotion\Models\OrderPromotion>
     */
    public static $model = \App\Modules\Promotion\Models\OrderPromotion::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'promotion.name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'promotion.name',
        'promotion.type',
    ];

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Promotion Name', function () {
                return $this->promotion?->name ?? 'Unknown Promotion';
            })->sortable(),

            Text::make('Promotion Type', function () {
                return $this->promotion?->type?->value ?? 'Unknown Type';
            })->sortable(),

            BelongsTo::make('Order', 'order', Order::class),
            BelongsTo::make('Promotion', 'promotion', Promotion::class),

            Code::make('Triggering Items', 'triggering_items')->json(),
            Code::make('Applied Rules', 'applied_rules')->json(),
            Code::make('Applied Benefits', 'applied_benefits')->json(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
