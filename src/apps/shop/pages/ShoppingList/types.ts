import { ProductType, StockStatusType, VendorType, OfferType } from '@/types';
import { UserType, ClinicType } from '@/types/common';

export type PreviouslyPurchasedResponse = {
  data: PreviouslyPurchasedItemType[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    currentPage: number;
    from: number;
    lastPage: number;
    path: string;
    perPage: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      page: number | null;
      active: boolean;
    }>;
  };
};

export type FetchPreviouslyPurchasedParams = {
  search?: string;
  perPage?: number;
  page?: number;
};

export type FetchPreviouslyPurchasedProps = {
  params: FetchPreviouslyPurchasedParams;
  beforeStart?: VoidFunction;
  onSuccess?: (data: {
    items: PreviouslyPurchasedItemType[];
    total: number;
  }) => void;
  onError?: VoidFunction;
  afterDone?: VoidFunction;
};

export type PreviouslyPurchasedItemType = {
  productOfferId: string;
  unitPrice: string;
  lastOrderedQuantity: number;
  lastOrderedAt: string;
  orderCount: number;
  product: Omit<
    ProductType,
    'isFavorite' | 'manufacturer' | 'manufacturerSku' | 'description'
  >;
  vendor: VendorType;
  stockStatus: StockStatusType;
  increments: number;
  isPurchasable: boolean;
};

export type ClinicShoppingListType = {
  id: string;
  clinicId: string;
  createdByUserId: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  clinic?: ClinicType;
  createdByUser?: UserType;
  items?: ShoppingListItemType[];
};

export type ShoppingListItemType = {
  id: string;
  clinicShoppingListId: string;
  productOfferId: string;
  createdByUserId: string;
  quantity: number;
  color: string | null;
  label: string | null;
  rank: number;
  createdAt: string;
  updatedAt: string;
  clinicShoppingList?: ClinicShoppingListType;
  product: ProductType;
  createdByUser?: UserType;
};

export type CreateShoppingListRequest = {
  clinicId: string;
  title: string;
};

export type UpdateShoppingListRequest = {
  title?: string;
};

export type CreateShoppingListItemRequest = {
  clinicShoppingListId: string;
  productOfferId: string;
  quantity: number;
  color?: string | null;
  label?: string | null;
  rank?: number;
};

export type UpdateShoppingListItemRequest = {
  quantity?: number;
  color?: string | null;
  label?: string | null;
  rank?: number;
};

export type ShoppingListResponse = {
  data: ClinicShoppingListType[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    currentPage: number;
    from: number;
    lastPage: number;
    path: string;
    perPage: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      page: number | null;
      active: boolean;
    }>;
  };
};

export type ShoppingListItemsResponse = {
  data: ShoppingListItemType[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    currentPage: number;
    from: number;
    lastPage: number;
    path: string;
    perPage: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      page: number | null;
      active: boolean;
    }>;
  };
};

export type FetchShoppingListsParams = {
  search?: string;
  perPage?: number;
  page?: number;
  clinicId?: string;
};

export type FetchShoppingListItemsParams = {
  search?: string;
  perPage?: number;
  page?: number;
  shoppingListId: string;
};
