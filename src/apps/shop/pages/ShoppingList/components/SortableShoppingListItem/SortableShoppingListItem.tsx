import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ShoppingListItem } from '../ShoppingListItem/ShoppingListItem';
import { ProductType } from '@/types';
import { Icon } from '@/libs/icons/Icon';
import { ShoppingListItemType } from '../../types';

type SortableShoppingListItemProps = {
  item: ShoppingListItemType;
  product: ProductType;
  isHighlighted?: boolean;
  variant?: 'customList' | 'previouslyPurchased';
  showPromoIcon?: boolean;
  showBadge?: boolean;
  badgeText?: string;
  badgeColor?: string;
  promoTooltip?: string;
  isDragDisabled?: boolean;
};

export const SortableShoppingListItem = ({
  product,
  isDragDisabled = false,
  ...shoppingListItemProps
}: SortableShoppingListItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: product.id,
    disabled: isDragDisabled,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} className="relative">
      <div className="flex items-center rounded-l-lg bg-white pl-2">
        <div
          {...attributes}
          {...listeners}
          className={`flex h-6 w-6 cursor-grab items-center justify-center text-gray-400 transition-colors hover:text-gray-600 active:cursor-grabbing ${isDragDisabled ? 'cursor-not-allowed opacity-50' : ''} ${isDragging ? 'cursor-grabbing' : ''} `}
          aria-label="Drag to reorder"
        >
          <Icon name="grip" size="1rem" />
        </div>

        <ShoppingListItem
          product={product}
          {...shoppingListItemProps}
          className="border-none"
        />
      </div>
    </div>
  );
};
