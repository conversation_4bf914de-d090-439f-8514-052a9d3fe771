import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { Icon } from '@/libs/icons/Icon';
import { getPriceString } from '@/utils';
import { AddToCart } from '@/libs/ui/AddToCart/AddToCart';
import { OfferType } from '@/types';
import { PurchaseHistoryChart } from '@/libs/products/components/PurchaseHistory/PurchaseHistoryChart';
import { Modal } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { Menu, MenuItem } from '@/libs/ui/Menu/Menu';
import { Button } from '@/libs/ui/Button/Button';
import { PreviouslyPurchasedItemType } from '../../types';

type ItemActionsProps = {
  product: PreviouslyPurchasedItemType['product'];
  onSwap: (newOfferId: string) => void;
  originalPrice: number;
  salePrice: number;
  currentOffer: OfferType;
  lastOrderedQuantity?: number;
};

export const ItemActions = ({
  product,
  onSwap,
  originalPrice,
  salePrice,
  currentOffer,
  lastOrderedQuantity,
}: ItemActionsProps) => {
  const [isModalOpen, { open, close }] = useDisclosure(false);
  const { offersMapData } = useCartStore();
  const cartOffer = offersMapData[currentOffer.id] || { quantity: 0 };

  return (
    <div className="flex items-center gap-2">
      <div className="flex h-16 min-w-lg items-center rounded-sm border border-cyan-100/25 bg-cyan-200 bg-gradient-to-t from-white/80 to-white/80 px-4">
        <VendorSwap
          currentOfferId={currentOffer.id}
          offers={product.offers}
          onSwap={onSwap}
          className="mr-0 h-8 bg-white"
        />
        <div className="divider-v mx-6 h-10"></div>
        <Icon
          name="graphics"
          size="3rem"
          color="#518EF8"
          onClick={open}
          className="cursor-pointer"
        />
        <Modal
          opened={isModalOpen}
          onClose={close}
          title="Purchase History"
          size="auto"
        >
          <div className="p-4">
            <PurchaseHistoryChart productId={currentOffer.id} />
          </div>
        </Modal>
        <div className="divider-v mr-0 ml-6 h-10"></div>
        {originalPrice > salePrice ? (
          <div className="flex w-32 flex-col items-center text-end">
            <span className="text-xs text-black/40 line-through">
              {getPriceString(cartOffer.quantity * originalPrice)}
            </span>
            <span className="text-[16px] leading-4 font-medium">
              {getPriceString(cartOffer.quantity * salePrice)}
            </span>
          </div>
        ) : (
          <div className="flex w-32 flex-col items-center text-end">
            <span className="text-[16px] leading-4 font-medium">
              {getPriceString(cartOffer.quantity * salePrice)}
            </span>
          </div>
        )}
        <AddToCart
          disabled={currentOffer.isPurchasable === false}
          productOfferId={currentOffer.id}
          minIncrement={currentOffer.increments || 1}
          className="w-28"
          size="sm"
          firstClickQty={lastOrderedQuantity}
        />
      </div>
      <Menu
        trigger={
          <Button
            variant="unstyled"
            aria-label="Vendor menu options"
            className="ml-auto"
          >
            <Icon
              name="moreOptions"
              color="#333"
              size="1.4rem"
              aria-hidden={true}
            />
          </Button>
        }
        side="bottom"
        align="end"
        sideOffset={2}
      >
        <MenuItem onClick={() => {}}>
          <p className="text-sm font-medium text-black">Remove from list</p>
        </MenuItem>
      </Menu>
    </div>
  );
};
