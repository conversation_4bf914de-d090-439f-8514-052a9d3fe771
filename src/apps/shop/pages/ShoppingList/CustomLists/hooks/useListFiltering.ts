import { useMemo } from 'react';
import { ClinicShoppingListType } from '../../types';

type UseListFilteringProps = {
  lists: ClinicShoppingListType[];
  searchQuery: string;
};

type UseListFilteringReturn = {
  filteredLists: (ClinicShoppingListType & { isVisible: boolean })[];
};

export const useListFiltering = ({
  lists,
  searchQuery,
}: UseListFilteringProps): UseListFilteringReturn => {
  const filteredLists = useMemo(() => {
    if (!searchQuery) {
      return lists.map((list) => ({
        ...list,
        isVisible: true,
        sortedItems: list.items,
      }));
    }

    return lists.map((list) => {
      const listNameMatches = list.title.toLowerCase().includes(searchQuery);

      if (listNameMatches) {
        return {
          ...list,
          isVisible: true,
          sortedItems: list.items,
        };
      }

      const hasMatchingProducts = list.items?.some((item) =>
        item.product.name.toLowerCase().includes(searchQuery),
      );

      return {
        ...list,
        isVisible: hasMatchingProducts,
      };
    });
  }, [lists, searchQuery]);

  return {
    filteredLists,
  };
};
