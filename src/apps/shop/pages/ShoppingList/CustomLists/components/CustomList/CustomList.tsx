import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { EmptyState } from './components/EmptyState/EmptyState';
import { ProductType } from '@/types';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { SortableShoppingListItem } from '../../../components/SortableShoppingListItem/SortableShoppingListItem';
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  restrictToVerticalAxis,
  restrictToParentElement,
} from '@dnd-kit/modifiers';
import { ClinicShoppingListType } from '../../../types';
import { LastUpdated } from '@/apps/shop/pages/Vendors/components/LastUpdated/LastUpdated';

type Props = {
  setActiveTab: (tab: number) => void;
  list: ClinicShoppingListType;
  searchQuery?: string;
  onReorderProducts?: (listName: string, newProducts: ProductType[]) => void;
};

export const CustomList = ({
  setActiveTab,
  list,
  searchQuery,
  onReorderProducts,
}: Props) => {
  const sensors = useSensors(useSensor(PointerSensor));

  // const handleDragEnd = (event: DragEndEvent) => {
  //   const { active, over } = event;

  //   if (active.id !== over?.id) {
  //     const oldIndex = list.items.findIndex(
  //       (product) => product.id === active.id,
  //     );
  //     const newIndex = list.items.findIndex(
  //       (product) => product.id === over?.id,
  //     );

  //     const newProducts = arrayMove(list.items, oldIndex, newIndex);
  //     onReorderProducts?.(list.name, newProducts);
  //   }
  // };

  return (
    <CollapsiblePanel
      variant="white"
      header={
        <div className="flex h-[72px] w-full items-center justify-between bg-white p-5 pr-16 pb-0">
          <Checkbox checked={false} onChange={() => {}} />
          <div className="ml-4 flex w-full flex-col gap-1">
            <span className="text-sm font-semibold">{list.title}</span>
            <div className="flex">
              <span className="text-xs text-black/50">
                Total of Items:{' '}
                <span className="font-semibold text-black/70">
                  {list.items?.length}
                </span>
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Created by:{' '}
                <span className="font-semibold text-black/70">
                  {list.createdByUser?.name}
                </span>
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Last Updated:{' '}
                <span className="font-semibold text-black/70">
                  <LastUpdated updatedAt={list.updatedAt} />
                </span>
              </span>
            </div>
          </div>
          {list.items && list.items.length > 0 ? (
            <Button className="w-40" size="sm" to={SHOP_ROUTES_PATH.checkout}>
              Checkout List
            </Button>
          ) : (
            <Button className="w-52" size="sm" onClick={() => setActiveTab(1)}>
              Add Products to List
            </Button>
          )}
        </div>
      }
      content={
        <div className="bg-white p-5">
          <div className="flex flex-col gap-1 bg-[#F8FBFD] p-4">
            {list.items && list.items.length > 0 ? (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={() => {}}
                modifiers={[restrictToVerticalAxis, restrictToParentElement]}
              >
                <SortableContext
                  items={list.items.map((product) => product.id)}
                  strategy={verticalListSortingStrategy}
                >
                  {list.items.map((item) => (
                    <SortableShoppingListItem
                      key={item.id}
                      item={item}
                      product={item.product}
                      variant="customList"
                      showPromoIcon={true}
                      showBadge={true}
                      badgeText="Surgery Room"
                      badgeColor="#ED1F22"
                      promoTooltip="Promotion Type: Bogo"
                      isHighlighted={
                        !!searchQuery &&
                        item.product.name
                          .toLocaleLowerCase()
                          .includes(searchQuery)
                      }
                    />
                  ))}
                </SortableContext>
              </DndContext>
            ) : (
              <EmptyState setActiveTab={setActiveTab} />
            )}
          </div>
          {!list.items ||
            (list.items.length === 0 && (
              <Button
                onClick={() => setActiveTab(1)}
                variant="unstyled"
                className="mt-4 w-full text-right text-blue-600"
              >
                <p className="mr-4 ml-auto">+ Add more products</p>
              </Button>
            ))}
        </div>
      }
      startOpen
    />
  );
};
