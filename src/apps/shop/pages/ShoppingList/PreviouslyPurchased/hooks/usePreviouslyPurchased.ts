import { useCallback, useState } from 'react';
import { fetchPreviouslyPurchased } from '../services/fetchPreviouslyPurchased';
import {
  FetchPreviouslyPurchasedParams,
  PreviouslyPurchasedItemType,
} from '../../types';

type UsePreviouslyPurchasedState = {
  items: PreviouslyPurchasedItemType[];
  total: number;
  isLoading: boolean;
  errorOnLoading: boolean;
};

type UsePreviouslyPurchasedReturn = UsePreviouslyPurchasedState & {
  fetchItems: (params: FetchPreviouslyPurchasedParams) => void;
};

export const usePreviouslyPurchased = (): UsePreviouslyPurchasedReturn => {
  const [state, setState] = useState<UsePreviouslyPurchasedState>({
    items: [],
    total: 0,
    isLoading: false,
    errorOnLoading: false,
  });

  const fetchItems = useCallback((params: FetchPreviouslyPurchasedParams) => {
    const { search, page = 1, perPage = 12 } = params;

    if (search === undefined || search === '' || search.length >= 3) {
      const apiParams = {
        search: search || undefined,
        page,
        perPage,
      };

      fetchPreviouslyPurchased({
        params: apiParams,
        beforeStart: () => {
          setState((prev) => ({
            ...prev,
            isLoading: true,
            errorOnLoading: false,
          }));
        },
        onSuccess: (data) => {
          setState((prev) => ({
            ...prev,
            items: data.items,
            total: data.total,
          }));
        },
        onError: () => {
          setState((prev) => ({ ...prev, errorOnLoading: true }));
        },
        afterDone: () => {
          setState((prev) => ({ ...prev, isLoading: false }));
        },
      });
    }
  }, []);

  return {
    ...state,
    fetchItems,
  };
};
