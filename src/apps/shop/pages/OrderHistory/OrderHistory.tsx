import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import { HeaderSearch } from '@/libs/ui/HeaderSearch/HeaderSearch';
import { Pagination } from '@/libs/ui/Pagination/Pagination';
import { getOrderHistoryItemUrl } from '@/apps/shop/routes/utils';
import { DateFilter } from '@/libs/date-filter/components/DateFilter/DateFilter';
import { useOrderList } from './services/useOrderList';

import { FEATURE_FLAGS } from '@/constants';
import { OrderHistoryContent } from './components/OrderHistoryContent/OrderHistoryContent';
import { OrderBy } from './components/OrderBy/OrderBy';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { NoResults } from '@/libs/ui/NoResults/NoResults';
import { BackorderedFilter } from './components/BackorderedFilter/BackorderedFilter';

export const OrderHistory = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { id: selectedOrderId } = useParams();
  const { orders, isLoading, total, queryParams } = useOrderList();

  useEffect(() => {
    const listIsLoaded = !isLoading && orders[0];
    const listIncludesSelectedOrder =
      listIsLoaded && orders.find((order) => order.id === selectedOrderId);

    if (listIsLoaded && !listIncludesSelectedOrder) {
      const firstItemUrl = getOrderHistoryItemUrl(orders[0].id);
      navigate(firstItemUrl);
    }
  }, [isLoading, orders, navigate, selectedOrderId]);

  const isResultFiltered =
    queryParams.query || queryParams.dateFromFilter || queryParams.dateToFilter;

  return (
    <div className={'mainSection'}>
      <PageHeader
        title={t('client.orderHistory.title')}
        description="Track items, and reconcile invoices"
      />

      <div className="mb-4 flex justify-between">
        <HeaderSearch
          initialValue={queryParams.query}
          onBlur={queryParams.setQuery}
          onEnter={queryParams.setQuery}
          placeholder={t('client.orderHistory.searchOrder')}
          size="md"
        />
        <div className="flex gap-2">
          <BackorderedFilter />
          <DateFilter
            applyedValues={{
              from: queryParams.dateFromFilter,
              to: queryParams.dateToFilter,
            }}
            onChange={queryParams.setDatesFilter}
          />
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <OrderBy setSort={queryParams.setSort} />
          )}
        </div>
      </div>

      {/* // TODO: Error on load */}
      {isLoading ? (
        <div className="relative min-h-[300px]">
          <ContentLoader />
        </div>
      ) : orders.length ? (
        <OrderHistoryContent
          orders={orders}
          selectedOrderId={selectedOrderId}
        />
      ) : (
        <NoResults
          label={!isResultFiltered ? 'No previous order history' : undefined}
        />
      )}

      <Pagination
        page={+(queryParams.page ?? 1)}
        onPageChange={(page) => queryParams.setPage(page.toString())}
        onChangeItemsPerPage={queryParams.setPerPage}
        itemsPerPage={queryParams.perPage}
        useUrlParams={true}
        limitOptions={[
          {
            value: '5',
            label: '5',
          },
          {
            value: '10',
            label: '10',
          },
          {
            value: '15',
            label: '15',
          },
          {
            value: '20',
            label: '20',
          },
        ]}
        total={total}
      />
    </div>
  );
};
