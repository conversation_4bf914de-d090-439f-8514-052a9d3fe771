import { useEffect } from 'react';
import { Button, Group, Text } from '@mantine/core';
import { useSearchParams } from 'react-router-dom';
import { Trans, useTranslation } from 'react-i18next';
import clsx from 'clsx';

import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import { Pagination } from '@/libs/ui/Pagination/Pagination';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { errorNotification, scrollToElement } from '@/utils';
import { getEndIndex, getStartIndex } from '@/libs/ui/Pagination/utils';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';

import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import type { SearchParamsProps, ProductType } from '@/types';
import styles from './search.module.css';
import { ProductSearchCardList } from './components/ProductSearchCardList/ProductSearchCardList';
import ArrowUpArrowDownIcon from '@/assets/images/product/arrow-down-arrow-up.svg?react';
import { FEATURE_FLAGS } from '@/constants';
import { SearchFilter } from './components/SearchFilter/SearchFilter';

export const Search = () => {
  const { t } = useTranslation();
  const [query, setQueryParams] = useSearchParams(
    new URLSearchParams(window.location.search),
  );

  const {
    getSearchProduct,
    productList,
    perPage,
    total,
    page,
    clearSearchProduct,
  } = useProductStore();

  const {
    apiRequest: handleSearch,
    isLoading: isSearchLoading,
    hasError,
  } = useAsyncRequest<Partial<SearchParamsProps<ProductType>>>({
    apiFunc: async (params) => {
      await getSearchProduct(params!, setQueryParams);
    },
    errorFunc: ({ apiResponse }) => {
      if (apiResponse?.status === 404) {
        clearSearchProduct();
      } else {
        errorNotification();
      }
    },
  });

  const searchValue = query.get('query')?.trim();
  const startIndex = getStartIndex(page, perPage);
  const endIndex = getEndIndex(startIndex, perPage, total);

  const handleChangeItemsPerPage = async (perPage: string) => {
    handleSearch({
      perPage,
      page: 1,
    });
    scrollToElement('searchScrollToTop');
  };

  const handleChangePage = async (value: number) => {
    handleSearch({
      page: value,
    });
    scrollToElement('searchScrollToTop');
  };

  const setHubSpotPageTracking = (reset: boolean = false) => {
    // @ts-expect-error - HubSpot global variable
    const _hsq = window._hsq || [];

    if (reset) {
      _hsq.push(['setPath', '/']);

      return;
    }

    _hsq.push(['setPath', SHOP_ROUTES_PATH.search]);
  };

  useEffect(() => {
    const params = Object.fromEntries(query);

    handleSearch(params);
    setHubSpotPageTracking();

    return () => {
      clearSearchProduct();
      setHubSpotPageTracking(true);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      id="searchScrollToTop"
      className={clsx(styles.container, 'mainSection')}
    >
      <PageHeader title={t('client.search.title')} />

      <div className={clsx(styles.content, 'pageContent')}>
        <div className={styles.contentTitle}>
          <div>
            <h3>Search Results</h3>
            <Text span size="lgMd">
              <Trans
                i18nKey="client.search.showingResults"
                values={{
                  totalPageStart: startIndex,
                  totalPageEnd: endIndex,
                  total,
                  searchValue,
                }}
                components={{ searchValue: <Text span fs="italic" /> }}
              />
            </Text>
          </div>

          <Group justify="center">
            {FEATURE_FLAGS.SEARCH_RESULTS_FILTER && (
              <SearchFilter
                onChange={(filters) => {
                  handleSearch(filters);
                }}
                initialValue={{ vendorIds: query.get('vendorIds') || '' }}
              />
            )}
            {FEATURE_FLAGS.SEARCH_RESULTS_ORDER && (
              <Button
                mr="10"
                leftSection={<ArrowUpArrowDownIcon />}
                variant="default"
              >
                Order by
              </Button>
            )}
          </Group>
        </div>

        <ProductSearchCardList
          searchValue={searchValue}
          productList={productList}
          isSearchLoading={isSearchLoading}
          hasError={hasError}
        />
      </div>

      <Pagination
        itemsPerPage={perPage}
        page={page}
        total={total}
        onPageChange={handleChangePage}
        onChangeItemsPerPage={handleChangeItemsPerPage}
        useUrlParams={true}
        limitOptions={[
          {
            value: '12',
            label: '12',
          },
          {
            value: '24',
            label: '24',
          },
          {
            value: '36',
            label: '36',
          },
          {
            value: '48',
            label: '48',
          },
        ]}
      />
    </div>
  );
};
