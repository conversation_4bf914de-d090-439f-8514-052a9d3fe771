<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ClinicWallet;
use App\Models\Vendor;
use App\Models\WalletDefinition;
use App\Models\WalletTransaction;
use App\Modules\Promotion\Engine\Actions\CashbackToWalletAction;
use App\Modules\Promotion\Engine\Context;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Models\PromotionAction;
use App\Modules\Promotion\Models\PromotionRule;
use App\Services\WalletService;

test('cashback promotion works with fixed amount', function () {
    // Setup
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create(['vendor_id' => $vendor->id]);

    $promotion = Promotion::create([
        'promotionable_type' => 'App\Modules\Gpo\Models\GpoAccount',
        'promotionable_id' => App\Modules\Gpo\Models\GpoAccount::factory()->create()->id,
        'vendor_id' => $vendor->id,
        'name' => 'Fixed Cashback Test',
        'type' => PromotionType::Cashback,
        'description' => 'Test fixed cashback',
        'priority' => 1,
        'started_at' => now(),
        'ended_at' => now()->addDays(30),
        'status' => 'active',
    ]);

    $promotionRule = PromotionRule::create(['promotion_id' => $promotion->id, 'priority' => 1]);

    $promotionAction = PromotionAction::create([
        'promotion_rule_id' => $promotionRule->id,
        'type' => ActionType::CashbackToWallet,
        'config' => [
            'wallet_definition_id' => $walletDefinition->id,
            'cashback_type' => 'fixed',
            'cashback_value' => 15.00, // $15.00
        ],
    ]);

    // Execute
    $context = new Context($clinic->id, null, 10000); // $100.00 order total
    $action = new CashbackToWalletAction(new WalletService());
    $action->apply($promotionRule, $context, $promotionAction->config);

    // Assert
    $wallet = ClinicWallet::where('clinic_id', $clinic->id)
        ->where('wallet_definition_id', $walletDefinition->id)
        ->first();

    expect($wallet)->not->toBeNull();
    expect($wallet->balance)->toBe(1500); // Fixed amount, not percentage

    $transaction = WalletTransaction::where('clinic_wallet_id', $wallet->id)
        ->where('type', 'credit')
        ->where('amount', 1500)
        ->where('status', 'approved')
        ->first();

    expect($transaction)->not->toBeNull();
    expect($transaction->description)->toContain($promotion->name);
    expect($transaction->metadata['cashback_type'])->toBe('fixed');
    expect($transaction->metadata['cashback_value'])->toBe(1500); // Still stored as cents in metadata
});

test('cashback promotion works with percentage amount', function () {
    // Setup
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create(['vendor_id' => $vendor->id]);

    $promotion = Promotion::create([
        'promotionable_type' => 'App\Modules\Gpo\Models\GpoAccount',
        'promotionable_id' => App\Modules\Gpo\Models\GpoAccount::factory()->create()->id,
        'vendor_id' => $vendor->id,
        'name' => 'Percentage Cashback Test',
        'type' => PromotionType::Cashback,
        'description' => 'Test percentage cashback',
        'priority' => 1,
        'started_at' => now(),
        'ended_at' => now()->addDays(30),
        'status' => 'active',
    ]);

    $promotionRule = PromotionRule::create(['promotion_id' => $promotion->id, 'priority' => 1]);

    $promotionAction = PromotionAction::create([
        'promotion_rule_id' => $promotionRule->id,
        'type' => ActionType::CashbackToWallet,
        'config' => [
            'wallet_definition_id' => $walletDefinition->id,
            'cashback_type' => 'percentage',
            'cashback_value' => 5.00, // 5%
        ],
    ]);

    // Execute
    $orderTotal = 10000; // $100.00
    $context = new Context($clinic->id, null, $orderTotal);
    $action = new CashbackToWalletAction(new WalletService());
    $action->apply($promotionRule, $context, $promotionAction->config);

    // Assert
    $wallet = ClinicWallet::where('clinic_id', $clinic->id)
        ->where('wallet_definition_id', $walletDefinition->id)
        ->first();

    expect($wallet)->not->toBeNull();
    expect($wallet->balance)->toBe(500); // 5% of $100.00 = $5.00

    $transaction = WalletTransaction::where('clinic_wallet_id', $wallet->id)
        ->where('type', 'credit')
        ->where('amount', 500)
        ->where('status', 'approved')
        ->first();

    expect($transaction)->not->toBeNull();
    expect($transaction->description)->toContain($promotion->name);
    expect($transaction->metadata['cashback_type'])->toBe('percentage');
    expect($transaction->metadata['cashback_value'])->toBe(500); // Still stored as basis points in metadata
    expect($transaction->metadata['order_total'])->toBe($orderTotal);
});

test('cashback promotion works with max cap for percentage', function () {
    // Setup
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create(['vendor_id' => $vendor->id]);

    $promotion = Promotion::create([
        'promotionable_type' => 'App\Modules\Gpo\Models\GpoAccount',
        'promotionable_id' => App\Modules\Gpo\Models\GpoAccount::factory()->create()->id,
        'vendor_id' => $vendor->id,
        'name' => 'Percentage with Cap Test',
        'type' => PromotionType::Cashback,
        'description' => 'Test percentage cashback with cap',
        'priority' => 1,
        'started_at' => now(),
        'ended_at' => now()->addDays(30),
        'status' => 'active',
    ]);

    $promotionRule = PromotionRule::create(['promotion_id' => $promotion->id, 'priority' => 1]);

    $promotionAction = PromotionAction::create([
        'promotion_rule_id' => $promotionRule->id,
        'type' => ActionType::CashbackToWallet,
        'config' => [
            'wallet_definition_id' => $walletDefinition->id,
            'cashback_type' => 'percentage',
            'cashback_value' => 10.00, // 10%
            'max_cashback' => 2000, // $20.00 cap
        ],
    ]);

    // Execute - Large order that would exceed cap
    $orderTotal = 50000; // $500.00 - 10% would be $50.00, but capped at $20.00
    $context = new Context($clinic->id, null, $orderTotal);
    $action = new CashbackToWalletAction(new WalletService());
    $action->apply($promotionRule, $context, $promotionAction->config);

    // Assert
    $wallet = ClinicWallet::where('clinic_id', $clinic->id)
        ->where('wallet_definition_id', $walletDefinition->id)
        ->first();

    expect($wallet)->not->toBeNull();
    expect($wallet->balance)->toBe(2000); // Capped at $20.00, not $50.00

    $transaction = WalletTransaction::where('clinic_wallet_id', $wallet->id)
        ->where('type', 'credit')
        ->where('amount', 2000)
        ->where('status', 'approved')
        ->first();

    expect($transaction)->not->toBeNull();
    expect($transaction->metadata['max_cashback'])->toBe(2000);
});

test('cashback promotion works with max cap for fixed amount', function () {
    // Setup
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create(['vendor_id' => $vendor->id]);

    $promotion = Promotion::create([
        'promotionable_type' => 'App\Modules\Gpo\Models\GpoAccount',
        'promotionable_id' => App\Modules\Gpo\Models\GpoAccount::factory()->create()->id,
        'vendor_id' => $vendor->id,
        'name' => 'Fixed with Cap Test',
        'type' => PromotionType::Cashback,
        'description' => 'Test fixed cashback with cap',
        'priority' => 1,
        'started_at' => now(),
        'ended_at' => now()->addDays(30),
        'status' => 'active',
    ]);

    $promotionRule = PromotionRule::create(['promotion_id' => $promotion->id, 'priority' => 1]);

    $promotionAction = PromotionAction::create([
        'promotion_rule_id' => $promotionRule->id,
        'type' => ActionType::CashbackToWallet,
        'config' => [
            'wallet_definition_id' => $walletDefinition->id,
            'cashback_type' => 'fixed',
            'cashback_value' => 30.00, // $30.00
            'max_cashback' => 2000, // $20.00 cap
        ],
    ]);

    // Execute
    $context = new Context($clinic->id, null, 10000);
    $action = new CashbackToWalletAction(new WalletService());
    $action->apply($promotionRule, $context, $promotionAction->config);

    // Assert
    $wallet = ClinicWallet::where('clinic_id', $clinic->id)
        ->where('wallet_definition_id', $walletDefinition->id)
        ->first();

    expect($wallet)->not->toBeNull();
    expect($wallet->balance)->toBe(2000); // Capped at $20.00, not $30.00

    $transaction = WalletTransaction::where('clinic_wallet_id', $wallet->id)
        ->where('type', 'credit')
        ->where('amount', 2000)
        ->where('status', 'approved')
        ->first();

    expect($transaction)->not->toBeNull();
    expect($transaction->metadata['max_cashback'])->toBe(2000);
});

test('cashback promotion handles existing wallet correctly', function () {
    // Setup
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create(['vendor_id' => $vendor->id]);

    // Create existing wallet with some balance
    $existingWallet = ClinicWallet::factory()->create([
        'clinic_id' => $clinic->id,
        'wallet_definition_id' => $walletDefinition->id,
        'balance' => 500, // $5.00
    ]);

    $promotion = Promotion::create([
        'promotionable_type' => 'App\Modules\Gpo\Models\GpoAccount',
        'promotionable_id' => App\Modules\Gpo\Models\GpoAccount::factory()->create()->id,
        'vendor_id' => $vendor->id,
        'name' => 'Existing Wallet Test',
        'type' => PromotionType::Cashback,
        'description' => 'Test with existing wallet',
        'priority' => 1,
        'started_at' => now(),
        'ended_at' => now()->addDays(30),
        'status' => 'active',
    ]);

    $promotionRule = PromotionRule::create(['promotion_id' => $promotion->id, 'priority' => 1]);

    $promotionAction = PromotionAction::create([
        'promotion_rule_id' => $promotionRule->id,
        'type' => ActionType::CashbackToWallet,
        'config' => [
            'wallet_definition_id' => $walletDefinition->id,
            'cashback_type' => 'fixed',
            'cashback_value' => 10.00, // $10.00
        ],
    ]);

    // Execute
    $context = new Context($clinic->id);
    $action = new CashbackToWalletAction(new WalletService());
    $action->apply($promotionRule, $context, $promotionAction->config);

    // Assert
    $wallet = $existingWallet->fresh();
    expect($wallet->balance)->toBe(1500); // $5.00 + $10.00 = $15.00

    $transaction = WalletTransaction::where('clinic_wallet_id', $wallet->id)
        ->where('type', 'credit')
        ->where('amount', 1000)
        ->where('status', 'approved')
        ->first();

    expect($transaction)->not->toBeNull();
});

test('cashback promotion handles invalid configuration gracefully', function () {
    // Setup
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $promotion = Promotion::create([
        'promotionable_type' => 'App\Modules\Gpo\Models\GpoAccount',
        'promotionable_id' => App\Modules\Gpo\Models\GpoAccount::factory()->create()->id,
        'vendor_id' => $vendor->id,
        'name' => 'Invalid Config Test',
        'type' => PromotionType::Cashback,
        'description' => 'Test invalid configuration',
        'priority' => 1,
        'started_at' => now(),
        'ended_at' => now()->addDays(30),
        'status' => 'active',
    ]);

    $promotionRule = PromotionRule::create(['promotion_id' => $promotion->id, 'priority' => 1]);

    // Create promotion action with invalid config (no wallet definition)
    $promotionAction = PromotionAction::create([
        'promotion_rule_id' => $promotionRule->id,
        'type' => ActionType::CashbackToWallet,
        'config' => [
            'cashback_type' => 'fixed',
            'cashback_value' => 10.00,
            // Missing wallet_definition_id
        ],
    ]);

    // Execute - should not throw exception
    $context = new Context($clinic->id);
    $action = new CashbackToWalletAction(new WalletService());
    $action->apply($promotionRule, $context, $promotionAction->config);

    // Assert - no wallet or transaction should be created
    $walletCount = ClinicWallet::where('clinic_id', $clinic->id)->count();
    expect($walletCount)->toBe(0);

    $transactionCount = WalletTransaction::count();
    expect($transactionCount)->toBe(0);
});

test('cashback promotion fails gracefully without order total for percentage', function () {
    // Setup
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create(['vendor_id' => $vendor->id]);

    $promotion = Promotion::create([
        'promotionable_type' => 'App\Modules\Gpo\Models\GpoAccount',
        'promotionable_id' => App\Modules\Gpo\Models\GpoAccount::factory()->create()->id,
        'vendor_id' => $vendor->id,
        'name' => 'No Order Total Test',
        'type' => PromotionType::Cashback,
        'description' => 'Test percentage without order total',
        'priority' => 1,
        'started_at' => now(),
        'ended_at' => now()->addDays(30),
        'status' => 'active',
    ]);

    $promotionRule = PromotionRule::create(['promotion_id' => $promotion->id, 'priority' => 1]);

    $promotionAction = PromotionAction::create([
        'promotion_rule_id' => $promotionRule->id,
        'type' => ActionType::CashbackToWallet,
        'config' => [
            'wallet_definition_id' => $walletDefinition->id,
            'cashback_type' => 'percentage',
            'cashback_value' => 5.00, // 5%
        ],
    ]);

    // Execute - without order total
    $context = new Context($clinic->id, null, null);
    $action = new CashbackToWalletAction(new WalletService());
    $action->apply($promotionRule, $context, $promotionAction->config);

    // Assert - no wallet should be created since cashback amount would be 0
    $wallet = ClinicWallet::where('clinic_id', $clinic->id)
        ->where('wallet_definition_id', $walletDefinition->id)
        ->first();

    expect($wallet)->toBeNull();
});
