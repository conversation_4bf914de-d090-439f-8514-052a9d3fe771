<?php

declare(strict_types=1);

use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\PromotionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Update promotion_actions.type constraint to include cashback_to_wallet
        // Handle both old and new constraint names that might exist
        DB::statement('ALTER TABLE promotion_actions DROP CONSTRAINT IF EXISTS promotion_actions_type_check');
        DB::statement('ALTER TABLE promotion_actions DROP CONSTRAINT IF EXISTS promotion_rewards_type_check');

        // Recreate the constraint with all valid action types
        DB::statement("ALTER TABLE promotion_actions ADD CONSTRAINT promotion_actions_type_check CHECK (type IN ('".ActionType::UpdateRebateEstimate->value."', '".ActionType::GiveFreeProduct->value."', '".ActionType::CashbackToWallet->value."'))");

        // Update promotions.type constraint to include cashback
        DB::statement('ALTER TABLE promotions DROP CONSTRAINT IF EXISTS promotions_type_check');
        DB::statement("ALTER TABLE promotions ADD CONSTRAINT promotions_type_check CHECK (type IN ('".PromotionType::Rebate->value."', '".PromotionType::BuyXGetY->value."', '".PromotionType::Cashback->value."'))");
    }
};
